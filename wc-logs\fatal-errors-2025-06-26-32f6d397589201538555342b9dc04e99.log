2025-06-26T11:01:25+00:00 CRITICAL Uncaught ValueError: sleep(): Argument #1 ($seconds) must be greater than or equal to 0 in /home/<USER>/public_html/wp-content/plugins/woocommerce-simple-auctions/woocommerce-simple-auctions.php:3699 CONTEXT: {"error":{"type":1,"file":"/home/<USER>/public_html/wp-content/plugins/woocommerce-simple-auctions/woocommerce-simple-auctions.php","line":3699},"remote-logging":true,"backtrace":["","#0 /home/<USER>/public_html/wp-content/plugins/woocommerce-simple-auctions/woocommerce-simple-auctions.php(3699): sleep(-61)","#1 /home/<USER>/public_html/wp-includes/class-wp-hook.php(324): WooCommerce_simple_auction->simple_auctions_cron('')","#2 /home/<USER>/public_html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)","#3 /home/<USER>/public_html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)","#4 /home/<USER>/public_html/wp-settings.php(727): do_action('init')","#5 /home/<USER>/public_html/wp-config.php(96): require_once('/home/<USER>/...')","#6 /home/<USER>/public_html/wp-load.php(50): require_once('/home/<USER>/...')","#7 /home/<USER>/public_html/wp-blog-header.php(16): require_once('/home/<USER>/...')","#8 /home/<USER>/public_html/index.php(17): require('/home/<USER>/...')","#9 {main}","thrown"]}
2025-06-26T11:01:26+00:00 CRITICAL Uncaught ValueError: sleep(): Argument #1 ($seconds) must be greater than or equal to 0 in /home/<USER>/public_html/wp-content/plugins/woocommerce-simple-auctions/woocommerce-simple-auctions.php:3699 CONTEXT: {"error":{"type":1,"file":"/home/<USER>/public_html/wp-content/plugins/woocommerce-simple-auctions/woocommerce-simple-auctions.php","line":3699},"remote-logging":true,"backtrace":["","#0 /home/<USER>/public_html/wp-content/plugins/woocommerce-simple-auctions/woocommerce-simple-auctions.php(3699): sleep(-4)","#1 /home/<USER>/public_html/wp-includes/class-wp-hook.php(324): WooCommerce_simple_auction->simple_auctions_cron('')","#2 /home/<USER>/public_html/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)","#3 /home/<USER>/public_html/wp-includes/plugin.php(517): WP_Hook->do_action(Array)","#4 /home/<USER>/public_html/wp-settings.php(727): do_action('init')","#5 /home/<USER>/public_html/wp-config.php(96): require_once('/home/<USER>/...')","#6 /home/<USER>/public_html/wp-load.php(50): require_once('/home/<USER>/...')","#7 /home/<USER>/public_html/wp-blog-header.php(16): require_once('/home/<USER>/...')","#8 /home/<USER>/public_html/index.php(17): require('/home/<USER>/...')","#9 {main}","thrown"]}
